#Tue Jul 18 17:43:39 GMT 2023
micronaut:
  application:
    name: ai-chatbot
  multitenancy:
    propagation:
      enabled: true
    tenantresolver:
      httpheader:
        enabled: true
        header-name: X-TENANT-ID
  netty:
    event-loops:
      other:
        num-threads: 10
  http:
    client:
      read-timeout: 30s
      connection-pool-idle-timeout: 10s
    server:
      read-timeout: 1m
  server:
    ssl:
      enabled: true
      buildSelfSigned: true
    netty:
      listeners:
        httpsListener:
          port: 8443
          ssl: true
      worker:
        event-loop-group: other
  executors:
    conversation-executor:
      type: fixed
      nThreads: 10
      thread-name-prefix: "conversation-pool-"
    message-handler:
      type: cached
      thread-name-prefix: "message-handler-pool-"

dynamodb:
  tableName: "Friday-AiChatHistory"

openai:
  token: FROM_SECRETS
  daily-log.token: FROM_SECRETS
  betaModel: gpt-4o
  model: gpt-4o
  connectionTimeout: 180
  transcription:
    model: whisper-1
    language: pt
    enabled: true

tracing:
  zipkin:
    enabled: false
    http:
      log-level: OFF

endpoints:
  health:
    enabled: true
    sensitive: false
    details-visible: ANONYMOUS
    disk-space:
      enabled: false
  metrics:
    enabled: true
    sensitive: false

integrations:
  wa-comm-centre:
    url: https://commcentre.friday.ai
  whatsapp:
    host: https://graph.facebook.com
  billPayment:
    secret: "bKzAm82UAVjUmKUBUpjKu5xUrjml5upVfazJeACYz4P4T2hPNyezrs3BTRmN"
    onePixPayPath: "/chatbotAI/one-pix-pay"
    pixQRCodePath: "/chatbotAI/pix-qr-code"
    listPendingBills: "/chatbotAI/pending-bills"
    balanceAndForecast: "/chatbotAI/balance-and-forecast"
    markBillsAsPaid: "/chatbotAI/mark-as-paid"
    markReminderAsDone: "/chatbotAI/mark-reminder-as-done"
    ignoreBills: "/chatbotAI/ignore"
    sendPix: "/chatbotAI/pix"
    retrySweepingTransfer: "/chatbotAI/retrySweepingTransfer"
    validatePix: "/chatbotAI/pix/validate"
    listContacts: "/chatbotAI/pixKeyContacts"
    subscriptionFeePath: "/chatbotAI/subscription/fee"
    subscriptionPath: "/chatbotAI/subscription"
    getPixLimitPath: "/chatbotAI/whatsAppPayment/{walletId}/limit"
    syncSendMessagePath: "/chatbotAI/send-message"
    scheduleBillsPath: "/chatbotAI/schedule"
    getActiveSweepingConsentsPath: "/chatbotAI/sweeping-consent"
    onboardingTestPixPath: "/chatbotAI/onboarding-test-pix"
    getSystemActivitiesPath: "/chatbotAI/systemActivities"
    setSystemActivityPath: "/chatbotAI/systemActivity"
    validatePixQrCodePath: "/chatbotAI/pix/qrcode/validate"
    addBillPath: "/chatbotAI/addBill"
    checkBillsPath: "/chatbotAI/checkBills"
    createManualEntryPath: "/chatbotAI/manualEntry"
    removeManualEntryPath: "/chatbotAI/manualEntry/{manualEntryId}"

shedlock:
  defaults:
    lock-at-most-for: 30m

aws:
  region: us-east-1
  transcription:
    language: pt-BR
    enabled: false
  sqs:
    region: ${aws.region}
    sqsWaitTime: 20
    sqsCoolDownTime: 0
    maxNumberOfMessages: 10
    visibilityTimeout: 300
    dlqEnabled: true
    queues:
      chatBotNotificationGateway: chatbot_notification_gateway
      chatBotWebhook: chatbot_webhooks
      chatbotStateUpdate: chatbot_state_update
      chatbotVerifyNotification: chatbot_verify_notification
      chatbotTemplateDelayQueue: chatbot_delayed_template_messages
      chatbotSimpleDelayQueue: chatbot_delayed_simple_messages
      waCommCentreIncomingMessageQueue: chatbot_incoming_message
      cognitoTokenMessageQueue: chatbot_cognito_token
      eventQueue: user_events

userEvents:
  enabled: true

chatbot-ai-transaction-auth:
  clientId: "CHATBOT_AI_TRANSACTION_CLIENT_ID-d502c3c4-0b1b-401f-aa75-e86d9f94cd95"
  secret: "5-H1f]QvL-_:uU1:Mk)J]v,s}q4=#UT=GJnoH%p4PnJX-nvjP0HFFPCBBPxA"

multiple-chat-message-handler:
  next-user-message-wait-time: 10s

# NOTE: Existe somente para inicializar os beans do comm-centre
email.bucket-unprocessed-emails: "ses-unprocessed-emails-via1"

# NOTE: Existe somenta para inicializar os beans do comm-centre
communication-centre:
  integration:
    blip:
      auth: xxx
      host: https://via1-pagamentos-digitais.http.msging.net
      namespace: 1d3afeae_c48c_4c2a_8d65_02b4bbf01f83
      command:
        path: /commands
      message:
        path: /messages

    billpayment:
      host: xxx
      username: xxx
      password: xxx
      createbill:
        path: xxx
  email:
    display-name: xxx
    return-path: xxx
    bucket-unprocessed-emails: xxx
    configuration-set-name: xxx
    receipt:
      email: xxx
      display-name: xxx
    notification:
      email: xxx
      display-name: xxx
    maxAttachments: 15
    max-pages-per-attachment: 15
    virus:
      bucket: quarantine-emails

tenant-default:
  notification-config:
    messages:
      authorizePix:
        params:
          - "{{RECIPIENT_NAME}}"
          - "{{RECIPIENT_DOCUMENT}}"
          - "{{RECIPIENT_INSTITUTION}}"
          - "{{AMOUNT}}"
          - "{{PIX_KEY}}"
        link.url: app/chatbot/autorizar-transacao/{{TRANSACTION_ID}}
        quickReplies:
          - action: TRANSACTION_CANCEL

      authorizePixSweeping:
        params:
          - "{{RECIPIENT_NAME}}"
          - "{{RECIPIENT_DOCUMENT}}"
          - "{{RECIPIENT_INSTITUTION}}"
          - "{{AMOUNT}}"
          - "{{PIX_KEY}}"
        link.url: app/chatbot/autorizar-transacao/{{TRANSACTION_ID}}
        quickReplies:
          - action: TRANSACTION_CANCEL

      authorizePixSweepingWithSubscription:
        params:
          - "{{RECIPIENT_NAME}}"
          - "{{RECIPIENT_DOCUMENT}}"
          - "{{RECIPIENT_INSTITUTION}}"
          - "{{AMOUNT}}"
          - "{{PIX_KEY}}"
        link.url: app/chatbot/autorizar-transacao/{{TRANSACTION_ID}}
        quickReplies:
          - action: TRANSACTION_CANCEL

      pixConfirmation:
        params:
          - "{{RECIPIENT_NAME}}"
          - "{{RECIPIENT_DOCUMENT}}"
          - "{{RECIPIENT_INSTITUTION}}"
          - "{{AMOUNT}}"
          - "{{PIX_KEY}}"
        quickReplies:
          - action: TRANSACTION_CONFIRM
          - action: TRANSACTION_CANCEL

      chatbotAiNotifyBillsComingDueSimple:
        params:
          - "{{USER_NAME}}"
        quickReplies:
          - action: SEND_SUBSCRIPTION_PIX_CODE

      chatbotAiNotifyBillsComingDueBasic:
        params:
          - "{{USER_NAME}}"
          - "{{TOTAL_BILLS}}"
        quickReplies:
          - action: SEND_REPLY

      chatbotAiNotifyBillsComingDueSingular:
        params:
          - "{{USER_NAME}}"
          - "{{BILL_DESCRIPTION}}"
          - "{{AMOUNT}}"
        quickReplies:
          - action: SCHEDULE_BILLS
          - action: MARK_AS_PAID

      chatbotAiNotifyBillsComingDueMax:
        params:
          - "{{USER_NAME}}"
          - "{{BILLS_LIST}}"
        quickReplies:
          - action: LIST_BILLS

      chatbotAiNotifyBillsComingDue:
        params:
          - "{{USER_NAME}}"
          - "{{BILLS_LIST}}"
        quickReplies:
          - action: SCHEDULE_BILLS
          - action: PAY_SOME
          - action: MARK_AS_PAID

      pixConfirmationSingleSweepingConsent:
        params:
          - "{{RECIPIENT_NAME}}"
          - "{{RECIPIENT_DOCUMENT}}"
          - "{{RECIPIENT_INSTITUTION}}"
          - "{{AMOUNT}}"
          - "{{PIX_KEY}}"
        quickReplies:
          - action: TRANSACTION_CONFIRM
          - action: TRANSACTION_CANCEL

      pixConfirmationMultipleSweepingConsent:
        text: |
          Você confirma os dados abaixo?
          
          👤 Nome: {{RECIPIENT_NAME}}
          🪪 Documento: {{RECIPIENT_DOCUMENT}}
          🏦 Instituição: {{RECIPIENT_INSTITUTION}}
          💰 Valor: {{AMOUNT}}
          🔑 Chave: {{PIX_KEY}}
        
          _Esta transferência usará saldo da sua conta conectada._
        params:
          - "{{RECIPIENT_NAME}}"
          - "{{RECIPIENT_DOCUMENT}}"
          - "{{RECIPIENT_INSTITUTION}}"
          - "{{AMOUNT}}"
          - "{{CONTA1_NOME}}"
          - "{{TRANSACTION_ID}}"
          - "{{CONTA2_NOME}}"
          - "{{TRANSACTION_ID_2}}"
        quickReplies:
          - text: Sim, usar {{CONTA1_NOME}}
            action: TRANSACTION_CONFIRM
            transactionId: TRANSACTION_ID
          - text: Sim, usar {{CONTA2_NOME}}
            action: TRANSACTION_CONFIRM
            transactionId: TRANSACTION_ID_2
          - text: Não
            action: TRANSACTION_CANCEL

      pixConfirmationSweepingWithSubscription:
        params:
          - "{{RECIPIENT_NAME}}"
          - "{{RECIPIENT_DOCUMENT}}"
          - "{{RECIPIENT_INSTITUTION}}"
          - "{{AMOUNT}}"
          - "{{PIX_KEY}}"
        quickReplies:
          - action: TRANSACTION_CONFIRM
          - action: TRANSACTION_CANCEL

      onboardingStart:
        quickReplies:
          - action: ONBOARDING_SINGLE_PIX
            payload: ACCEPT

      sweepingAccountConfirmation:
        quickReplies:
          - action: SWEEPING_SCHEDULE_BILLS
          - action: SEND_PIX_CODE

      waitingApprovalBillsSingular:
        params:
          - "{{TOTAL_BILLS}}"
        link.url: contas

      waitingApprovalBillsPlural:
        params:
          - "{{TOTAL_BILLS}}"
        link.url: contas

      createManualEntryErrorNoAmount:
        text: "Entendi que você quer registrar um gasto para *{{TITLE}}*. Poderia me informar o valor?"

      createManualEntryErrorNoTitle:
        text: "Entendi que você quer registrar um gasto de *{{AMOUNT}}*. Poderia me informar um título para esse registro?"

      createManualEntryErrorNoAmountAndTitle:
        text: "Entendi que você quer registrar um gasto. Poderia me informar o valor e o título do gasto que você deseja registrar?"

      createManualEntrySuccess:
        text: "Registrei um gasto de *{{AMOUNT}}* com *{{TITLE}}* para você."
        quickReplies:
          - text: "Desfazer"
            action: "REMOVE_MANUAL_ENTRY"
            payload: "{{MANUAL_ENTRY_ID}}"

      removeManualEntrySuccess:
        text: "Ok, removi esse item da sua timeline pra você."

      createReminderErrorNoDate:
        text: "Entendi que você quer criar um lembrete para *{{TITLE}}*. Poderia me informar a data que você gostaria de ser lembrado desse pagamento?"

      createReminderErrorNoTitle:
        text: "Entendi que você quer criar um lembrete para *{{DATE}}*. Poderia me informar um título para esse lembrete?"

      createReminderErrorNoTitleAndDate:
        text: "Entendi que você quer criar um lembrete. Poderia me informar um título e uma data para que eu possa te lembrar no dia?"

      createReminderErrorDateInPast:
        text: "Entendi que você quer criar um lembrete para *{{DATE}}*, mas preciso que a data do lembrete seja no futuro."

      createReminderSuccess:
        text: |
          Criei um lembrete para você:
          
          *Título*: {{TITLE}}
          *Data*: {{DATE}}
          *Valor*: {{AMOUNT}}
          
          Vou te lembrar desse pagamento no início do dia!
        quickReplies:
          - text: "Desfazer"
            action: "REMOVE_MANUAL_ENTRY"
            payload: "{{MANUAL_ENTRY_ID}}"

      createReminderSuccessNoAmount:
        text: |
          Criei um lembrete para você:
          
          *Título*: {{TITLE}}
          *Data*: {{DATE}}
          *Valor*: -
          
          _Você pode editar o valor desse lembrete quando quiser pelo app._
          
          Vou te lembrar desse pagamento no início do dia!
        quickReplies:
          - text: "Desfazer"
            action: "REMOVE_MANUAL_ENTRY"
            payload: "{{MANUAL_ENTRY_ID}}"

  feature-flags.values:
      user-events-http:
        enabled: NO_ONE
      send-via-wa-comm-centre:
        enabled: NO_ONE
      wa-comm-centre:
        enabled: NO_ONE
      daily-log-s3:
        enabled: NO_ONE